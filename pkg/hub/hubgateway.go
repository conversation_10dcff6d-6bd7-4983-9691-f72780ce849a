package hub

import (
	"context"
	"time"
)

// HubGateway is the interface that developers implement to provide the central
// request handling and routing logic on the Hub side.
// mctunnel core provides the streams, and the HubGateway implementation
// decides what to do with them.
type HubGateway interface {
	// --- Lifecycle Management ---

	// Run starts the Hub service, which is a blocking call.
	// It listens for gRPC connections and handles tunnel lifecycle.
	// The server will initiate a graceful shutdown if the provided context is canceled.
	Run(ctx context.Context) error

	// Shutdown gracefully terminates the Hub service.
	// It stops accepting new tunnels and attempts to drain existing ones
	// before closing all connections. The provided context can set a timeout
	// for the shutdown process.
	Shutdown(ctx context.Context) error

	// --- Data Plane ---

	// NewStream creates a new logical stream to a specific agent, identified by its cluster name.
	// This is the primary method used by developers' gateways to proxy requests.
	NewStream(ctx context.Context, clusterName string) error

	// --- State Introspection ---

	// ListTunnels returns a list of all currently active and registered agent tunnels.
	ListTunnels() []TunnelInfo

	// GetTunnel returns information about a specific tunnel.
	// Returns an error if no tunnel exists for the given clusterName.
	GetTunnel(clusterName string) (TunnelInfo, error)

	// Ready returns true if the Hub is fully initialized and ready to serve traffic.
	// This is intended for use in readiness probes.
	Ready() bool
}

// TunnelInfo provides a snapshot of a single active agent tunnel's metadata.
type TunnelInfo struct {
	ClusterName   string
	AgentID       string
	ConnectedAt   time.Time
	ActiveStreams int64
	LastHeartbeat time.Time
}
