package agent

import (
	"context"
	"fmt"
	"net"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	v1 "github.com/xuezhaojun/multiclustertunnel/api/v1"
)

// 配置测试：
// TestDefaultProxyManagerConfig - 默认配置验证
// TestNewProxyManager - 基本构造函数
// TestNewProxyManagerWithConfig - 自定义配置
// TestCustomConfig - 自定义配置验证
// 核心功能测试：
// TestDispatchDataPacket_NewConnection - 新连接创建
// TestDispatchDataPacket_ExistingConnection - 现有连接复用
// TestDispatchErrorPacket - 错误包处理
// TestDispatchUnknownControlCode - 未知控制码处理
// 错误处理测试：
// TestTargetResolverError - 目标解析错误
// TestConnectionDialError - 连接拨号错误
// TestConnectionCleanupOnError - 连接清理
// 高级功能测试：
// TestOutgoingDataFlow - 出站数据流
// TestMultipleConnections - 多连接管理
// TestProxyManagerClose - 优雅关闭
// TestConcurrentDispatch - 并发处理

// mockTargetResolver implements TargetResolver for testing
type mockTargetResolver struct {
	targetAddr string
	err        error
}

func (m *mockTargetResolver) GetTargetAddress(packet *v1.Packet) (string, error) {
	if m.err != nil {
		return "", m.err
	}
	return m.targetAddr, nil
}

// mockServer creates a simple TCP server for testing
type mockServer struct {
	listener net.Listener
	addr     string
	data     []byte
	mu       sync.Mutex
	closed   bool
}

func newMockServer() (*mockServer, error) {
	listener, err := net.Listen("tcp", "127.0.0.1:0")
	if err != nil {
		return nil, err
	}

	server := &mockServer{
		listener: listener,
		addr:     listener.Addr().String(),
	}

	go server.serve()
	return server, nil
}

func (s *mockServer) serve() {
	for {
		conn, err := s.listener.Accept()
		if err != nil {
			return
		}

		go func(c net.Conn) {
			defer c.Close()

			// Echo server: read data and write it back
			buffer := make([]byte, 1024)
			for {
				n, err := c.Read(buffer)
				if err != nil {
					return
				}

				// Store received data for verification
				s.mu.Lock()
				s.data = append(s.data, buffer[:n]...)
				s.mu.Unlock()

				// Echo back
				_, err = c.Write(buffer[:n])
				if err != nil {
					return
				}
			}
		}(conn)
	}
}

func (s *mockServer) getReceivedData() []byte {
	s.mu.Lock()
	defer s.mu.Unlock()
	return append([]byte(nil), s.data...)
}

func (s *mockServer) close() {
	s.mu.Lock()
	defer s.mu.Unlock()
	if !s.closed {
		s.listener.Close()
		s.closed = true
	}
}

func TestDefaultProxyManagerConfig(t *testing.T) {
	config := DefaultProxyManagerConfig()

	assert.Equal(t, connReadBufferSize, config.ReadBufferSize)
	assert.Equal(t, outgoingChanSize, config.OutgoingChanSize)
	assert.Equal(t, dialTimeout, config.DialTimeout)
}

func TestNewProxyManager(t *testing.T) {
	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: "127.0.0.1:8080"}

	pm := newProxyManager(ctx, resolver)
	require.NotNil(t, pm)

	// Test that outgoing channel is available
	outgoing := pm.OutgoingChan()
	require.NotNil(t, outgoing)

	// Clean up
	err := pm.Close()
	assert.NoError(t, err)
}

func TestNewProxyManagerWithConfig(t *testing.T) {
	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: "127.0.0.1:8080"}

	config := &ProxyManagerConfig{
		ReadBufferSize:   16 * 1024,
		OutgoingChanSize: 50,
		DialTimeout:      5 * time.Second,
	}

	pm := newProxyManagerWithConfig(ctx, resolver, config)
	require.NotNil(t, pm)

	// Verify config is used
	impl := pm.(*proxyManagerImpl)
	assert.Equal(t, config, impl.config)

	// Clean up
	err := pm.Close()
	assert.NoError(t, err)
}

func TestDispatchDataPacket_NewConnection(t *testing.T) {
	// Start mock server
	server, err := newMockServer()
	require.NoError(t, err)
	defer server.close()

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	// Create a data packet
	packet := &v1.Packet{
		ConnId: 1,
		Code:   v1.ControlCode_DATA,
		Data:   []byte("hello world"),
	}

	// Dispatch the packet
	err = pm.Dispatch(packet)
	assert.NoError(t, err)

	// Wait a bit for connection to be established and data to be sent
	time.Sleep(100 * time.Millisecond)

	// Verify data was received by mock server
	receivedData := server.getReceivedData()
	assert.Equal(t, []byte("hello world"), receivedData)
}

func TestDispatchDataPacket_ExistingConnection(t *testing.T) {
	// Start mock server
	server, err := newMockServer()
	require.NoError(t, err)
	defer server.close()

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	// Create first packet to establish connection
	packet1 := &v1.Packet{
		ConnId: 1,
		Code:   v1.ControlCode_DATA,
		Data:   []byte("first"),
	}

	err = pm.Dispatch(packet1)
	assert.NoError(t, err)

	// Wait for connection to be established
	time.Sleep(50 * time.Millisecond)

	// Send second packet on same connection
	packet2 := &v1.Packet{
		ConnId: 1,
		Code:   v1.ControlCode_DATA,
		Data:   []byte("second"),
	}

	err = pm.Dispatch(packet2)
	assert.NoError(t, err)

	// Wait for data to be sent
	time.Sleep(100 * time.Millisecond)

	// Verify both packets were received
	receivedData := server.getReceivedData()
	assert.Equal(t, []byte("firstsecond"), receivedData)
}

func TestDispatchErrorPacket(t *testing.T) {
	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: "127.0.0.1:8080"}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	// Create an error packet
	packet := &v1.Packet{
		ConnId:       1,
		Code:         v1.ControlCode_ERROR,
		ErrorMessage: "test error",
	}

	// Dispatch the packet
	err := pm.Dispatch(packet)
	assert.NoError(t, err)

	// Verify connection is not created for error packets
	impl := pm.(*proxyManagerImpl)
	impl.connLock.RLock()
	assert.Empty(t, impl.connections)
	impl.connLock.RUnlock()
}

func TestDispatchUnknownControlCode(t *testing.T) {
	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: "127.0.0.1:8080"}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	// Create packet with unknown control code
	packet := &v1.Packet{
		ConnId: 1,
		Code:   v1.ControlCode(999), // Invalid code
		Data:   []byte("test"),
	}

	// Dispatch should return error
	err := pm.Dispatch(packet)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unknown control code")
}

func TestTargetResolverError(t *testing.T) {
	ctx := context.Background()
	resolver := &mockTargetResolver{
		err: fmt.Errorf("resolver error"),
	}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	packet := &v1.Packet{
		ConnId: 1,
		Code:   v1.ControlCode_DATA,
		Data:   []byte("test"),
	}

	err := pm.Dispatch(packet)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to get target endpoint")
}

func TestConnectionDialError(t *testing.T) {
	ctx := context.Background()
	resolver := &mockTargetResolver{
		targetAddr: "127.0.0.1:99999", // Invalid port
	}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	packet := &v1.Packet{
		ConnId: 1,
		Code:   v1.ControlCode_DATA,
		Data:   []byte("test"),
	}

	err := pm.Dispatch(packet)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to dial target")
}

func TestOutgoingDataFlow(t *testing.T) {
	// Start mock server
	server, err := newMockServer()
	require.NoError(t, err)
	defer server.close()

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	// Create a data packet to establish connection
	packet := &v1.Packet{
		ConnId: 1,
		Code:   v1.ControlCode_DATA,
		Data:   []byte("test"),
	}

	err = pm.Dispatch(packet)
	require.NoError(t, err)

	// Wait for connection and echo response
	time.Sleep(100 * time.Millisecond)

	// Check outgoing channel for echoed data
	outgoing := pm.OutgoingChan()
	select {
	case outPacket := <-outgoing:
		assert.Equal(t, int64(1), outPacket.ConnId)
		assert.Equal(t, v1.ControlCode_DATA, outPacket.Code)
		assert.Equal(t, []byte("test"), outPacket.Data)
	case <-time.After(1 * time.Second):
		t.Fatal("Expected outgoing packet but got timeout")
	}
}

func TestMultipleConnections(t *testing.T) {
	// Start mock server
	server, err := newMockServer()
	require.NoError(t, err)
	defer server.close()

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	// Create packets for different connections
	packet1 := &v1.Packet{
		ConnId: 1,
		Code:   v1.ControlCode_DATA,
		Data:   []byte("conn1"),
	}

	packet2 := &v1.Packet{
		ConnId: 2,
		Code:   v1.ControlCode_DATA,
		Data:   []byte("conn2"),
	}

	// Dispatch both packets
	err = pm.Dispatch(packet1)
	require.NoError(t, err)

	err = pm.Dispatch(packet2)
	require.NoError(t, err)

	// Wait for connections to be established
	time.Sleep(100 * time.Millisecond)

	// Verify both connections exist
	impl := pm.(*proxyManagerImpl)
	impl.connLock.RLock()
	assert.Len(t, impl.connections, 2)
	assert.Contains(t, impl.connections, int64(1))
	assert.Contains(t, impl.connections, int64(2))
	impl.connLock.RUnlock()
}

func TestConnectionCleanupOnError(t *testing.T) {
	// Start mock server
	server, err := newMockServer()
	require.NoError(t, err)

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	// Create a data packet to establish connection
	packet := &v1.Packet{
		ConnId: 1,
		Code:   v1.ControlCode_DATA,
		Data:   []byte("test"),
	}

	err = pm.Dispatch(packet)
	require.NoError(t, err)

	// Wait for connection to be established
	time.Sleep(50 * time.Millisecond)

	// Verify connection exists
	impl := pm.(*proxyManagerImpl)
	impl.connLock.RLock()
	assert.Len(t, impl.connections, 1)
	impl.connLock.RUnlock()

	// Send an ERROR packet to trigger cleanup
	errorPacket := &v1.Packet{
		ConnId:       1,
		Code:         v1.ControlCode_ERROR,
		ErrorMessage: "simulated error",
	}

	err = pm.Dispatch(errorPacket)
	require.NoError(t, err)

	// Wait a bit for cleanup
	time.Sleep(100 * time.Millisecond)

	// Verify connection was cleaned up
	impl.connLock.RLock()
	assert.Empty(t, impl.connections)
	impl.connLock.RUnlock()

	// Close the server
	server.close()
}

func TestProxyManagerClose(t *testing.T) {
	// Start mock server
	server, err := newMockServer()
	require.NoError(t, err)
	defer server.close()

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}
	pm := newProxyManager(ctx, resolver)

	// Create connections
	packet1 := &v1.Packet{ConnId: 1, Code: v1.ControlCode_DATA, Data: []byte("test1")}
	packet2 := &v1.Packet{ConnId: 2, Code: v1.ControlCode_DATA, Data: []byte("test2")}

	err = pm.Dispatch(packet1)
	require.NoError(t, err)
	err = pm.Dispatch(packet2)
	require.NoError(t, err)

	// Wait for connections
	time.Sleep(50 * time.Millisecond)

	// Verify connections exist
	impl := pm.(*proxyManagerImpl)
	impl.connLock.RLock()
	assert.Len(t, impl.connections, 2)
	impl.connLock.RUnlock()

	// Close proxy manager
	err = pm.Close()
	assert.NoError(t, err)

	// Verify all connections are cleaned up
	impl.connLock.RLock()
	assert.Empty(t, impl.connections)
	impl.connLock.RUnlock()

	// Verify outgoing channel is closed
	// We need to drain any remaining packets first
	outgoing := pm.OutgoingChan()
	for {
		select {
		case _, ok := <-outgoing:
			if !ok {
				// Channel is closed, this is what we expect
				return
			}
			// Continue draining
		case <-time.After(100 * time.Millisecond):
			t.Fatal("Expected closed channel but got timeout")
		}
	}
}

func TestConcurrentDispatch(t *testing.T) {
	// Start mock server
	server, err := newMockServer()
	require.NoError(t, err)
	defer server.close()

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	// Dispatch packets concurrently
	const numGoroutines = 10
	const packetsPerGoroutine = 5

	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			defer wg.Done()
			for j := 0; j < packetsPerGoroutine; j++ {
				packet := &v1.Packet{
					ConnId: int64(goroutineID*packetsPerGoroutine + j + 1),
					Code:   v1.ControlCode_DATA,
					Data:   []byte(fmt.Sprintf("data-%d-%d", goroutineID, j)),
				}
				err := pm.Dispatch(packet)
				assert.NoError(t, err)
			}
		}(i)
	}

	wg.Wait()

	// Wait for all connections to be established
	time.Sleep(200 * time.Millisecond)

	// Verify all connections were created
	impl := pm.(*proxyManagerImpl)
	impl.connLock.RLock()
	expectedConnections := numGoroutines * packetsPerGoroutine
	assert.Len(t, impl.connections, expectedConnections)
	impl.connLock.RUnlock()
}

func TestCustomConfig(t *testing.T) {
	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: "127.0.0.1:8080"}

	config := &ProxyManagerConfig{
		ReadBufferSize:   8 * 1024, // 8KB
		OutgoingChanSize: 25,       // Smaller buffer
		IncomingChanSize: 10,       // Smaller incoming buffer
		DialTimeout:      1 * time.Second,
	}

	pm := newProxyManagerWithConfig(ctx, resolver, config)
	defer pm.Close()

	impl := pm.(*proxyManagerImpl)
	assert.Equal(t, 8*1024, impl.config.ReadBufferSize)
	assert.Equal(t, 25, impl.config.OutgoingChanSize)
	assert.Equal(t, 10, impl.config.IncomingChanSize)
	assert.Equal(t, 1*time.Second, impl.config.DialTimeout)
}

// TestPacketOrderingForSameConnection tests that packets with the same conn_id
// are processed sequentially to maintain TCP ordering semantics
func TestPacketOrderingForSameConnection(t *testing.T) {
	// Start mock server that records received data in order
	server, err := newMockServer()
	require.NoError(t, err)
	defer server.close()

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	connID := int64(1)
	numPackets := 10
	packetSize := 100

	// Send multiple packets with the same conn_id rapidly
	for i := 0; i < numPackets; i++ {
		// Create packet with sequential data
		data := make([]byte, packetSize)
		for j := 0; j < packetSize; j++ {
			data[j] = byte(i) // Each packet has data filled with its sequence number
		}

		packet := &v1.Packet{
			ConnId: connID,
			Code:   v1.ControlCode_DATA,
			Data:   data,
		}

		err := pm.Dispatch(packet)
		assert.NoError(t, err)
	}

	// Wait for all packets to be processed
	time.Sleep(200 * time.Millisecond)

	// Verify that data was received in the correct order
	receivedData := server.getReceivedData()
	expectedLength := numPackets * packetSize
	assert.Equal(t, expectedLength, len(receivedData), "Should receive all data")

	// Verify the order - each 100-byte chunk should have the same byte value
	for i := 0; i < numPackets; i++ {
		start := i * packetSize
		end := start + packetSize
		chunk := receivedData[start:end]

		// All bytes in this chunk should be the same (the packet sequence number)
		expectedByte := byte(i)
		for j, b := range chunk {
			assert.Equal(t, expectedByte, b,
				"Packet %d, byte %d should be %d but got %d - packets may be out of order",
				i, j, expectedByte, b)
		}
	}
}

// Benchmark tests
func BenchmarkDispatchNewConnection(b *testing.B) {
	server, err := newMockServer()
	require.NoError(b, err)
	defer server.close()

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		packet := &v1.Packet{
			ConnId: int64(i + 1),
			Code:   v1.ControlCode_DATA,
			Data:   []byte("benchmark data"),
		}
		err := pm.Dispatch(packet)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkDispatchExistingConnection(b *testing.B) {
	server, err := newMockServer()
	require.NoError(b, err)
	defer server.close()

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	// Establish connection first
	packet := &v1.Packet{
		ConnId: 1,
		Code:   v1.ControlCode_DATA,
		Data:   []byte("initial"),
	}
	err = pm.Dispatch(packet)
	require.NoError(b, err)

	time.Sleep(50 * time.Millisecond) // Wait for connection

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		packet := &v1.Packet{
			ConnId: 1,
			Code:   v1.ControlCode_DATA,
			Data:   []byte("benchmark data"),
		}
		err := pm.Dispatch(packet)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// TestConcurrentConnectionLimits tests the maximum number of concurrent connections
func TestConcurrentConnectionLimits(t *testing.T) {
	// Start mock server
	server, err := newMockServer()
	require.NoError(t, err)
	defer server.close()

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}
	pm := newProxyManager(ctx, resolver)
	defer pm.Close()

	// Test different connection counts to find the limit
	testCases := []struct {
		name        string
		connections int
		expectError bool
	}{
		{"100 connections", 100, false},
		{"500 connections", 500, false},
		{"1000 connections", 1000, false},
		{"2000 connections", 2000, false},
		{"5000 connections", 5000, true}, // Expect this to fail
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create a fresh proxy manager for each test
			pmTest := newProxyManager(ctx, resolver)
			defer pmTest.Close()

			successCount := 0
			errorCount := 0

			// Create connections concurrently
			var wg sync.WaitGroup
			var mu sync.Mutex

			for i := 0; i < tc.connections; i++ {
				wg.Add(1)
				go func(connID int) {
					defer wg.Done()

					packet := &v1.Packet{
						ConnId: int64(connID + 1),
						Code:   v1.ControlCode_DATA,
						Data:   []byte(fmt.Sprintf("data-%d", connID)),
					}

					err := pmTest.Dispatch(packet)

					mu.Lock()
					if err != nil {
						errorCount++
					} else {
						successCount++
					}
					mu.Unlock()
				}(i)
			}

			wg.Wait()

			// Wait for connections to be established
			time.Sleep(500 * time.Millisecond)

			t.Logf("Test: %s", tc.name)
			t.Logf("Success: %d, Errors: %d, Total: %d", successCount, errorCount, tc.connections)
			t.Logf("Success Rate: %.2f%%", float64(successCount)/float64(tc.connections)*100)

			if tc.expectError {
				assert.Greater(t, errorCount, 0, "Expected some errors for high connection count")
			} else {
				// Allow some errors due to system limits, but most should succeed
				successRate := float64(successCount) / float64(tc.connections)
				assert.Greater(t, successRate, 0.8, "Success rate should be > 80%")
			}

			// Check actual connections created
			impl := pmTest.(*proxyManagerImpl)
			impl.connLock.RLock()
			actualConnections := len(impl.connections)
			impl.connLock.RUnlock()

			t.Logf("Actual connections created: %d", actualConnections)
		})
	}
}

// TestConnectionLimitProgressive finds the exact connection limit progressively
func TestConnectionLimitProgressive(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping connection limit test in short mode")
	}

	// Start mock server
	server, err := newMockServer()
	require.NoError(t, err)
	defer server.close()

	ctx := context.Background()
	resolver := &mockTargetResolver{targetAddr: server.addr}

	// Binary search to find the limit
	low := 100
	high := 10000
	maxSuccessful := 0

	t.Logf("Starting binary search for connection limit between %d and %d", low, high)

	for low <= high {
		mid := (low + high) / 2

		pm := newProxyManager(ctx, resolver)

		successCount := 0
		var wg sync.WaitGroup
		var mu sync.Mutex

		// Test with 'mid' connections
		for i := 0; i < mid; i++ {
			wg.Add(1)
			go func(connID int) {
				defer wg.Done()

				packet := &v1.Packet{
					ConnId: int64(connID + 1),
					Code:   v1.ControlCode_DATA,
					Data:   []byte("test"),
				}

				err := pm.Dispatch(packet)
				if err == nil {
					mu.Lock()
					successCount++
					mu.Unlock()
				}
			}(i)
		}

		wg.Wait()
		time.Sleep(200 * time.Millisecond)

		successRate := float64(successCount) / float64(mid)
		t.Logf("Testing %d connections: %d successful (%.2f%%)", mid, successCount, successRate*100)

		pm.Close()

		if successRate > 0.95 { // 95% success rate
			maxSuccessful = mid
			low = mid + 1
		} else {
			high = mid - 1
		}

		// Small delay between tests to let system recover
		time.Sleep(100 * time.Millisecond)
	}

	t.Logf("Maximum successful concurrent connections: %d", maxSuccessful)

	// Verify the result with a final test
	if maxSuccessful > 0 {
		t.Run("verify_limit", func(t *testing.T) {
			pm := newProxyManager(ctx, resolver)
			defer pm.Close()

			successCount := 0
			var wg sync.WaitGroup
			var mu sync.Mutex

			for i := 0; i < maxSuccessful; i++ {
				wg.Add(1)
				go func(connID int) {
					defer wg.Done()

					packet := &v1.Packet{
						ConnId: int64(connID + 1),
						Code:   v1.ControlCode_DATA,
						Data:   []byte("verify"),
					}

					err := pm.Dispatch(packet)
					if err == nil {
						mu.Lock()
						successCount++
						mu.Unlock()
					}
				}(i)
			}

			wg.Wait()
			time.Sleep(200 * time.Millisecond)

			successRate := float64(successCount) / float64(maxSuccessful)
			t.Logf("Verification: %d/%d connections successful (%.2f%%)",
				successCount, maxSuccessful, successRate*100)

			assert.Greater(t, successRate, 0.9, "Verification should have >90% success rate")
		})
	}
}
