package agent

import (
	"context"
	"fmt"
	"io"
	"net"
	"sync"
	"time"

	v1 "github.com/xuezhaojun/multiclustertunnel/api/v1"
	"k8s.io/klog/v2"
)

const (
	// outgoingChanSize is the buffer size for the outgoing packet channel
	outgoingChanSize = 100
	// incomingChanSize is the buffer size for each connection's incoming packet channel
	// This ensures packets with the same conn_id are processed sequentially while
	// allowing buffering to handle network fluctuations and prevent blocking
	incomingChanSize = 50
	// connReadBufferSize is the buffer size for reading from local connections
	// 32KB is a good balance between memory usage and performance for most use cases:
	// - Small enough to avoid excessive memory usage
	// - Large enough to reduce syscall overhead
	// - Suitable for typical API requests/responses
	// For high-throughput scenarios, consider increasing to 64KB or 128KB
	connReadBufferSize = 32 * 1024 // 32KB
	// dialTimeout is the timeout for dialing local services
	dialTimeout = 10 * time.Second
)

// ProxyManagerConfig holds configuration for the proxy manager
type ProxyManagerConfig struct {
	// ReadBufferSize is the buffer size for reading from local connections
	// Default: 32KB, recommended range: 16KB-128KB
	ReadBufferSize int
	// OutgoingChanSize is the buffer size for the outgoing packet channel
	// Default: 100, recommended range: 50-500
	OutgoingChanSize int
	// IncomingChanSize is the buffer size for each connection's incoming packet channel
	// Default: 50, recommended range: 20-200
	IncomingChanSize int
	// DialTimeout is the timeout for dialing local services
	// Default: 10s, recommended range: 5s-30s
	DialTimeout time.Duration
}

// DefaultProxyManagerConfig returns the default configuration
func DefaultProxyManagerConfig() *ProxyManagerConfig {
	return &ProxyManagerConfig{
		ReadBufferSize:   connReadBufferSize,
		OutgoingChanSize: outgoingChanSize,
		IncomingChanSize: incomingChanSize,
		DialTimeout:      dialTimeout,
	}
}

// proxyManager receives tunnel.Packet from Hub and manages local connections
type proxyManager interface {
	Dispatch(packet *v1.Packet) error
	OutgoingChan() <-chan *v1.Packet
	Close() error
}

// connection represents a single local connection managed by the proxy manager
type connection struct {
	id       int64
	conn     net.Conn
	ctx      context.Context
	cancel   context.CancelFunc
	outgoing chan<- *v1.Packet
	// incoming is the channel for packets from Hub that need to be processed sequentially
	// This ensures packets with the same conn_id are processed in order
	incoming chan *v1.Packet
}

type proxyManagerImpl struct {
	targetResolver TargetResolver
	config         *ProxyManagerConfig
	connections    map[int64]*connection
	connLock       sync.RWMutex
	outgoing       chan *v1.Packet
	ctx            context.Context
	cancel         context.CancelFunc
}

func newProxyManager(ctx context.Context, targetResolver TargetResolver) proxyManager {
	return newProxyManagerWithConfig(ctx, targetResolver, DefaultProxyManagerConfig())
}

func newProxyManagerWithConfig(ctx context.Context, targetResolver TargetResolver, config *ProxyManagerConfig) proxyManager {
	ctx, cancel := context.WithCancel(ctx)
	return &proxyManagerImpl{
		targetResolver: targetResolver,
		config:         config,
		connections:    make(map[int64]*connection),
		outgoing:       make(chan *v1.Packet, config.OutgoingChanSize),
		ctx:            ctx,
		cancel:         cancel,
	}
}

// Dispatch handles incoming packets from the Hub
func (p *proxyManagerImpl) Dispatch(packet *v1.Packet) error {
	switch packet.Code {
	case v1.ControlCode_DATA:
		return p.handleDataPacket(packet)
	case v1.ControlCode_ERROR:
		return p.handleErrorPacket(packet)
	default:
		return fmt.Errorf("unknown control code: %v", packet.Code)
	}
}

// OutgoingChan returns the channel for outgoing packets to the Hub
func (p *proxyManagerImpl) OutgoingChan() <-chan *v1.Packet {
	return p.outgoing
}

// Close gracefully shuts down the proxy manager
func (p *proxyManagerImpl) Close() error {
	p.cancel()

	// Close all active connections
	p.connLock.Lock()
	for _, conn := range p.connections {
		conn.cancel()
		conn.conn.Close()
	}
	p.connections = make(map[int64]*connection)
	p.connLock.Unlock()

	// Close the outgoing channel
	close(p.outgoing)

	return nil
}

// handleDataPacket processes DATA packets from the Hub
// This method is now non-blocking and dispatches packets to per-connection channels
func (p *proxyManagerImpl) handleDataPacket(packet *v1.Packet) error {
	connID := packet.ConnId

	p.connLock.RLock()
	conn, exists := p.connections[connID]
	p.connLock.RUnlock()

	if !exists {
		// This is a new connection, create it
		return p.createConnection(packet)
	}

	// Send packet to connection's incoming channel for sequential processing
	// This is non-blocking to avoid affecting other connections
	select {
	case conn.incoming <- packet:
		return nil
	case <-conn.ctx.Done():
		// Connection is being closed
		return fmt.Errorf("connection %d is closing", connID)
	case <-p.ctx.Done():
		// ProxyManager is being closed
		return fmt.Errorf("proxy manager is closing")
	default:
		// Channel is full, this indicates a problem with the connection
		// or the target service is too slow to process data
		klog.Warningf("Connection incoming channel is full, dropping packet for conn_id %d", connID)
		return fmt.Errorf("connection %d incoming channel is full", connID)
	}
}

// handleErrorPacket processes ERROR packets from the Hub
func (p *proxyManagerImpl) handleErrorPacket(packet *v1.Packet) error {
	connID := packet.ConnId

	// Log the error
	klog.ErrorS(fmt.Errorf("%s", packet.ErrorMessage), "Received error from Hub", "conn_id", connID)

	// Close the connection if it exists
	p.removeConnection(connID)

	return nil
}

// createConnection establishes a new connection to the target service
func (p *proxyManagerImpl) createConnection(packet *v1.Packet) error {
	connID := packet.ConnId

	// Get target endpoint from ServiceProxy
	targetAddr, err := p.targetResolver.GetTargetAddress(packet)
	if err != nil {
		return fmt.Errorf("failed to get target endpoint for conn_id %d: %w", connID, err)
	}

	// Dial the target service
	conn, err := net.DialTimeout("tcp", targetAddr, p.config.DialTimeout)
	if err != nil {
		return fmt.Errorf("failed to dial target %s for conn_id %d: %w", targetAddr, connID, err)
	}

	// Create connection context
	ctx, cancel := context.WithCancel(p.ctx)

	// Create connection object with incoming packet channel
	connection := &connection{
		id:       connID,
		conn:     conn,
		ctx:      ctx,
		cancel:   cancel,
		outgoing: p.outgoing,
		incoming: make(chan *v1.Packet, p.config.IncomingChanSize),
	}

	// Store the connection
	p.connLock.Lock()
	p.connections[connID] = connection
	p.connLock.Unlock()

	// Start goroutine to read from the connection and send data back to Hub
	go p.readFromConnection(connection)

	// Start goroutine to process incoming packets sequentially for this connection
	go p.processIncomingPackets(connection)

	// Send the initial packet to the connection's incoming channel
	select {
	case connection.incoming <- packet:
	case <-ctx.Done():
		p.removeConnection(connID)
		return fmt.Errorf("failed to send initial packet to connection %d: context cancelled", connID)
	}

	klog.V(4).InfoS("Created new connection", "conn_id", connID, "target", targetAddr)
	return nil
}

// removeConnection closes and removes a connection
func (p *proxyManagerImpl) removeConnection(connID int64) {
	p.connLock.Lock()
	defer p.connLock.Unlock()

	conn, exists := p.connections[connID]
	if !exists {
		return
	}

	// Cancel the connection context and close the connection
	conn.cancel()
	conn.conn.Close()

	// Close the incoming channel to signal the processing goroutine to exit
	close(conn.incoming)

	// Remove from map
	delete(p.connections, connID)

	klog.V(4).InfoS("Removed connection", "conn_id", connID)
}

// readFromConnection reads data from a local connection and sends it to the Hub
func (p *proxyManagerImpl) readFromConnection(conn *connection) {
	defer p.removeConnection(conn.id)

	buffer := make([]byte, p.config.ReadBufferSize)

	for {
		select {
		case <-conn.ctx.Done():
			return
		default:
			// Set read deadline to avoid blocking forever
			conn.conn.SetReadDeadline(time.Now().Add(time.Second))

			n, err := conn.conn.Read(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					// Timeout is expected, continue reading
					continue
				}
				if err == io.EOF {
					klog.V(4).InfoS("Connection closed by remote", "conn_id", conn.id)
				} else {
					klog.ErrorS(err, "Error reading from connection", "conn_id", conn.id)
				}
				return
			}

			if n > 0 {
				// Send data back to Hub
				packet := &v1.Packet{
					ConnId: conn.id,
					Code:   v1.ControlCode_DATA,
					Data:   make([]byte, n),
				}
				copy(packet.Data, buffer[:n])

				select {
				case conn.outgoing <- packet:
				case <-conn.ctx.Done():
					return
				case <-p.ctx.Done():
					return
				}
			}
		}
	}
}

// processIncomingPackets processes packets from Hub sequentially for a specific connection
// This ensures that packets with the same conn_id are processed in order
func (p *proxyManagerImpl) processIncomingPackets(conn *connection) {
	defer func() {
		klog.V(4).InfoS("Stopped processing incoming packets", "conn_id", conn.id)
	}()

	klog.V(4).InfoS("Started processing incoming packets", "conn_id", conn.id)

	for {
		select {
		case packet, ok := <-conn.incoming:
			if !ok {
				// Channel is closed, connection is being removed
				return
			}

			// Process the packet by writing data to the target connection
			if len(packet.Data) > 0 {
				_, err := conn.conn.Write(packet.Data)
				if err != nil {
					klog.ErrorS(err, "Failed to write data to target connection", "conn_id", conn.id)
					// Connection failed, clean it up
					p.removeConnection(conn.id)
					return
				}
			}

		case <-conn.ctx.Done():
			// Connection context is cancelled
			return
		case <-p.ctx.Done():
			// ProxyManager context is cancelled
			return
		}
	}
}
