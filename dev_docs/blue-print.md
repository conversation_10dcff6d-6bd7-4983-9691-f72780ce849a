随着讨论的深入，确保我们拥有一个统一、全面、稳定的“共识文档”至关重要。这能避免我们在未来的讨论中产生分歧或遗忘关键决策。

我将把我们至今为止所有的核心讨论、设计决策、权衡取舍以及最终结论，整理成一份详细、结构化的设计纲要。这份文档将作为我们项目的“单一事实来源 (Single Source of Truth)”，足以支撑后续的设计和开发工作。

multiclustertunnel 项目设计纲要 (v1.0)

1. 概述与愿景 (Overview & Vision)

multiclustertunnel 是一个以 Go 语言库（pkg）形式提供的高性能、云原生多集群隧道框架。

核心使命: 赋能开发者，使其能够轻松构建自定义的、安全可靠的、用于打通 Hub 端与多个处于隔离网络中的 Managed Cluster 之间网络通信的解决方案。

设计哲学:框架而非产品: 提供强大的底层隧道和流管理能力，但将上层业务逻辑（如认证、路由、应用协议适配）的控制权完全交给开发者。

云原生亲和: 优先利用 Kubernetes 等云原生平台提供的能力（如服务发现、负载均衡、高可用机制），而不是重新发明轮子。

高可扩展性: 通过清晰的接口设计，允许开发者轻松集成和扩展，以适应不同的业务场景。

2. 核心架构 (Core Architecture)

项目的核心数据流如下：

外部客户端 -> [开发者实现的 HubGateway] -> [mctunnel Hub 核心] <--- gRPC Tunnel ---> [mctunnel Agent 核心] -> [开发者实现的 ServiceProxy] -> 目标服务

mctunnel 核心层 (Core Layer):Hub Core (pkg/hub): 运行在中心 Hub 端，负责管理 gRPC Server，维护所有 Agent 的连接隧道，并提供创建逻辑流 (PacketStream) 的能力。

Agent Core (pkg/agent): 运行在 managed cluster 中，负责作为 gRPC 客户端连接到 Hub，维持隧道的健康和重连，并根据 Hub 的指令，将数据流委托给 ServiceProxy 处理。

开发者实现层 (Developer Implementation Layer):HubGateway: 开发者构建的应用。它作为流量入口，负责处理外部客户端的请求，实现认证授权，并调用 Hub Core 的 API 来创建数据流。

ServiceProxy: 开发者通过实现 agent.ServiceProxy 接口提供的业务逻辑。它负责解析来自 Hub 的请求，连接到集群内的具体服务，并在本地代理数据。

3. 协议定义 (Protocol Definition)

我们采用一个自定义的、基于 gRPC 双向流的协议。协议是整个项目的基石，定义在 api/v1/ 目录下，以拥抱云原生社区的最佳实践。

文件路径: api/v1/tunnel.proto

Go 包路径: github.com/your/multiclustertunnel/api/v1

最终协议 (tunnel.proto):

Protocol Buffers

`syntax = "proto3";

package tunnel.v1;

option go_package = "github.com/your/multiclustertunnel/api/v1";

service TunnelService {

rpc Tunnel(stream Packet) returns (stream Packet) {}

}

enum ControlCode {

DATA = 0; // 业务数据包

STREAM_EOF = 1; // 逻辑流正常结束

STREAM_ERROR = 2; // 逻辑流发生错误

PING = 3; // Hub -> Agent 心跳

PONG = 4; // Agent -> Hub 心跳响应

DRAIN = 5; // 优雅关闭前的排空指令

}

message Packet {

int64 stream_id = 1; // 逻辑流 ID，用于多路复用

ControlCode code = 2; // 包的意图

bytes data = 3; // 业务数据载荷

map<string, string> headers = 4; // 应用层元数据 (路由、追踪 ID 等)

string error_message = 5; // 错误信息

}`

4. 项目结构与交付模型

mctunnel 作为一个框架，其结构和交付模型与 Konnectivity 有根本不同。

交付模型: 库 (Package) 而非 二进制文件 (Binary)。开发者通过 import 使用，而非直接运行。这提供了极致的灵活性。

目录结构:

mctunnel/ ├── api/v1/ # API 协议定义 ├── pkg/ │ ├── hub/ # Hub 端核心库 │ ├── agent/ # Agent 端核心库 │ └── stream/ # 核心的 PacketStream 抽象 ├── examples/ # 完整的、可运行的示例 └── internal/ # 内部实现细节 (如 StreamManager)

5. 高可用 (HA) 模型

我们采用“平台原生式 HA”，将复杂性委托给 Kubernetes。

Hub 端 (Active-Active):将 HubGateway 应用部署为一个多副本的 Deployment。

在 Deployment 前面创建一个 K8s Service (LoadBalancer/NodePort)。

所有 Agent 都连接到这个 Service 的稳定地址，由 K8s 负责负载均衡和故障切换。

Agent 端 (Active-Passive):将 ServiceProxy 应用部署为一个多副本的 Deployment。

使用 K8s 的 Leader Election 机制（如 client-go 提供的库）。

任何时候只有一个 pod (Leader) 会成功获取 Lease 锁，并执行连接 Hub 的逻辑。其他 pod (Followers) 处于备用状态。

当 Leader 死亡，Lease 释放，一个 Follower 会成为新的 Leader 并接管，重建隧道。

结论: 这种模式极大地简化了 Agent 的代码逻辑，将 HA 的复杂性交给了稳定可靠的 K8s 平台。

6. 核心接口与开发者体验

这是连接 mctunnel 核心与开发者逻辑的桥梁。

核心抽象 (pkg/stream/stream.go):

Go

type PacketStream interface { Context() context.Context ID() int64 Recv() <-chan *v1.Packet Send(packet *v1.Packet) error Close(err error) }

Agent 端接口 (pkg/agent/adapter.go): 开发者实现此接口。

Go

type ServiceProxy interface { ForwardStream(s stream.PacketStream, dialPacket \*v1.Packet) }

Hub 端接口 (pkg/hub/hub.go): 开发者使用此接口。

Go

type Hub interface { Run(ctx context.Context) error Shutdown(ctx context.Context) error NewStream(ctx context.Context, clusterName string) (stream.PacketStream, error) ListTunnels() []TunnelInfo Ready() bool }

7. Agent 实现：弹性与健壮性

为确保 Agent 在恶劣网络下的稳定性，其实现必须包含：

一个永不退出的 Run 主循环，负责管理 Agent 的生命周期。

在连接失败后，采用带随机抖动 (Jitter) 的指数退避 (Exponential Backoff) 策略进行重连。

显式配置和启用 gRPC Keep-Alive 机制，以检测和清理僵尸连接。

一个 serveSession 函数，通过两个并发的 goroutine 处理上行和下行数据，并通过 gRPC 的流控机制实现天然的背压传导。

通过监听 context 的取消事件，并配合 Shutdown 接口，实现优雅关闭。

8. 与 Konnectivity/ANP 的核心差异总结

对比维度 ANP (Konnectivity)mctunnel (我们的设计)核心定位产品 / 解决方案框架 / 平台交付方式二进制文件 (Binary)Go 语言库 (Package)HA 模型 Hub 端 Active-Active，Agent 是重客户端 Hub/Agent 均可 HA，依赖云原生平台能力路由逻辑负载均衡 (Random)，服务于等价 Agent 池精确寻址 (Deterministic)，服务于具名 Agent 身份与多租户单租户、高信任模型多租户、零信任模型，需开发者实现 AuthN/Z 协议抽象层次网络层抽象 (远程 Dial)应用层抽象 (转发带上下文的请求)拓扑管理内嵌、固化的管理逻辑委托、可扩展，由开发者在 HubGateway 中实现这份文档凝聚了我们所有的讨论成果。它清晰地定义了 mctunnel 的架构、理念、协议和接口，足以作为我们未来所有设计和开发工作的坚实基础和统一蓝图。
